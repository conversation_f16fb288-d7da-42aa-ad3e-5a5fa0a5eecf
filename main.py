#!/usr/bin/env python3
"""
PassChanger - AI-based Password Security Management System
Main application entry point
"""

import asyncio
import logging
import sys
import signal
from pathlib import Path
from typing import Optional

import yaml
from rich.console import Console
from rich.logging import RichHandler
from rich.panel import Panel
from rich.text import Text

from src.leak_detector import LeakDetector
from src.ai_engine import AIEngine
from src.database import DatabaseManager
from src.security_utils import SecurityManager
from src.tor_manager import TorManager

console = Console()

class PassChangerApp:
    def __init__(self, config_path: str = "config.yaml"):
        self.config_path = config_path
        self.config = None
        self.db_manager = None
        self.security_manager = None
        self.ai_engine = None
        self.leak_detector = None
        self.tor_manager = None
        self.running = False

    def load_config(self) -> dict:
        """Load application configuration"""
        try:
            with open(self.config_path, 'r') as f:
                self.config = yaml.safe_load(f)
            return self.config
        except FileNotFoundError:
            console.print(f"[red]Configuration file {self.config_path} not found![/red]")
            sys.exit(1)
        except yaml.YAMLError as e:
            console.print(f"[red]Error parsing configuration: {e}[/red]")
            sys.exit(1)

    def setup_logging(self):
        """Setup application logging"""
        log_level = getattr(logging, self.config['app']['log_level'], logging.INFO)

        # Create logs directory if it doesn't exist
        Path("logs").mkdir(exist_ok=True)

        # Setup rich logging
        logging.basicConfig(
            level=log_level,
            format="%(message)s",
            datefmt="[%X]",
            handlers=[
                RichHandler(rich_tracebacks=True),
                logging.FileHandler(self.config['monitoring']['log_file'])
            ]
        )

        self.logger = logging.getLogger("PassChanger")

    async def initialize_components(self):
        """Initialize all application components"""
        try:
            # Initialize database
            self.db_manager = DatabaseManager(self.config)
            await self.db_manager.initialize()

            # Initialize security manager
            self.security_manager = SecurityManager(self.config)
            await self.security_manager.initialize()

            # Initialize AI engine
            self.ai_engine = AIEngine(self.config)
            await self.ai_engine.initialize()

            # Initialize Tor manager if darkweb scanning is enabled
            if self.config['leak_detection']['sources']['darkweb']['enabled']:
                self.tor_manager = TorManager(self.config)
                await self.tor_manager.initialize()

            # Initialize leak detector
            self.leak_detector = LeakDetector(
                self.config,
                self.ai_engine,
                self.db_manager,
                self.tor_manager
            )
            await self.leak_detector.initialize()

            self.logger.info("All components initialized successfully")

        except Exception as e:
            self.logger.error(f"Failed to initialize components: {e}")
            raise

    def display_banner(self):
        """Display application banner"""
        banner_text = Text()
        banner_text.append("PassChanger", style="bold blue")
        banner_text.append(" v", style="white")
        banner_text.append(self.config['app']['version'], style="green")
        banner_text.append("\nAI-Powered Password Security Management", style="italic")

        panel = Panel(
            banner_text,
            title="🔐 Security Monitor",
            border_style="blue",
            padding=(1, 2)
        )
        console.print(panel)

    async def run_leak_detection(self):
        """Run leak detection scan"""
        console.print("\n[yellow]Starting leak detection scan...[/yellow]")

        try:
            results = await self.leak_detector.scan_for_leaks()

            if results:
                console.print(f"[red]⚠️  Found {len(results)} potential security issues![/red]")
                for result in results:
                    console.print(f"  • {result['description']}")
                    console.print(f"    Risk Level: {result['risk_level']}")
                    console.print(f"    Source: {result['source']}")
                    console.print()
            else:
                console.print("[green]✅ No security issues detected[/green]")

        except Exception as e:
            console.print(f"[red]Error during leak detection: {e}[/red]")
            self.logger.error(f"Leak detection failed: {e}")

    async def interactive_mode(self):
        """Run in interactive mode"""
        while self.running:
            console.print("\n[bold]PassChanger Menu:[/bold]")
            console.print("1. Run leak detection scan")
            console.print("2. View account status")
            console.print("3. View current threat lists for account")
            console.print("4. Add new account")
            console.print("5. Settings")
            if self.tor_manager:
                console.print("6. Tor management")
                console.print("7. Exit")
                max_option = 7
            else:
                console.print("6. Exit")
                max_option = 6

            try:
                choice = console.input(f"\nSelect option (1-{max_option}): ").strip()

                if choice == "1":
                    await self.run_leak_detection()
                elif choice == "2":
                    await self.show_account_status()
                elif choice == "3":
                    await self.show_account_threats()
                elif choice == "4":
                    await self.add_account()
                elif choice == "5":
                    await self.show_settings()
                elif choice == "6" and self.tor_manager:
                    await self.tor_management_menu()
                elif choice == str(max_option):
                    self.running = False
                    console.print("[yellow]Shutting down...[/yellow]")
                else:
                    console.print("[red]Invalid option. Please try again.[/red]")

            except KeyboardInterrupt:
                self.running = False
                console.print("\n[yellow]Shutting down...[/yellow]")
            except Exception as e:
                console.print(f"[red]Error: {e}[/red]")

    async def show_account_status(self):
        """Show status of monitored accounts"""
        accounts = await self.db_manager.get_all_accounts()

        if not accounts:
            console.print("[yellow]No accounts configured yet.[/yellow]")
            return

        console.print("\n[bold]Account Status:[/bold]")
        for account in accounts:
            status_color = "green" if account['status'] == 'secure' else "red"
            console.print(f"  • {account['name']}: [{status_color}]{account['status']}[/{status_color}]")

    async def show_account_threats(self):
        """Show current threat lists for a specific account"""
        accounts = await self.db_manager.get_all_accounts()

        if not accounts:
            console.print("[yellow]No accounts configured yet.[/yellow]")
            return

        # Display available accounts
        console.print("\n[bold]Select Account:[/bold]")
        for i, account in enumerate(accounts, 1):
            risk_color = "red" if account['risk_level'] in ['high', 'critical'] else "yellow" if account['risk_level'] == 'medium' else "green"
            console.print(f"  {i}. {account['name']} ({account['email']}) - Risk: [{risk_color}]{account['risk_level'] or 'unknown'}[/{risk_color}]")

        try:
            choice = console.input(f"\nSelect account (1-{len(accounts)}): ").strip()
            account_index = int(choice) - 1

            if 0 <= account_index < len(accounts):
                selected_account = accounts[account_index]
                await self._display_account_threats(selected_account)
            else:
                console.print("[red]Invalid selection.[/red]")

        except ValueError:
            console.print("[red]Please enter a valid number.[/red]")
        except Exception as e:
            console.print(f"[red]Error: {e}[/red]")

    async def _display_account_threats(self, account):
        """Display threats for a specific account"""
        console.print(f"\n[bold]Threat Analysis for: {account['name']} ({account['email']})[/bold]")

        # Get current threats (unresolved)
        threats = await self.db_manager.get_account_detections(account['id'], include_resolved=False)

        if not threats:
            console.print("[green]✅ No current threats detected for this account[/green]")
            return

        console.print(f"[yellow]⚠️  Found {len(threats)} active threat(s):[/yellow]\n")

        for i, threat in enumerate(threats, 1):
            # Determine risk color
            risk_color = "red" if threat['risk_level'] in ['high', 'critical'] else "yellow" if threat['risk_level'] == 'medium' else "blue"

            console.print(f"[bold]{i}. {threat['description']}[/bold]")
            console.print(f"   Source: {threat['source']}")
            console.print(f"   Type: {threat['detection_type']}")
            console.print(f"   Risk Level: [{risk_color}]{threat['risk_level']}[/{risk_color}]")
            console.print(f"   Confidence: {threat['confidence']:.1%}")
            console.print(f"   Detected: {threat['detected_at']}")
            console.print(f"   Status: {threat['status'] or 'new'}")

            # Show AI analysis if available
            if threat.get('ai_analysis') and isinstance(threat['ai_analysis'], dict):
                ai_analysis = threat['ai_analysis']
                if ai_analysis.get('summary'):
                    console.print(f"   AI Analysis: {ai_analysis['summary']}")
                if ai_analysis.get('recommendations'):
                    console.print(f"   Recommendations: {ai_analysis['recommendations']}")

            console.print()  # Empty line between threats

        # Ask if user wants to see resolved threats too
        show_resolved = console.input("Show resolved threats too? (y/N): ").strip().lower()
        if show_resolved in ['y', 'yes']:
            resolved_threats = await self.db_manager.get_account_detections(account['id'], include_resolved=True)
            resolved_only = [t for t in resolved_threats if t['status'] == 'resolved']

            if resolved_only:
                console.print(f"\n[dim]Resolved threats ({len(resolved_only)}):[/dim]")
                for threat in resolved_only:
                    console.print(f"  • {threat['description']} (resolved: {threat['resolved_at']})")
            else:
                console.print("\n[dim]No resolved threats found.[/dim]")

    async def add_account(self):
        """Add a new account to monitor"""
        console.print("\n[bold]Add New Account:[/bold]")

        name = console.input("Account name: ").strip()
        email = console.input("Email address: ").strip()
        username = console.input("Username (optional): ").strip()
        category = console.input("Category (critical/important/standard/low): ").strip()

        if not name or not email:
            console.print("[red]Name and email are required![/red]")
            return

        try:
            await self.db_manager.add_account({
                'name': name,
                'email': email,
                'username': username or None,
                'category': category or 'standard'
            })
            console.print(f"[green]✅ Account '{name}' added successfully[/green]")
        except Exception as e:
            console.print(f"[red]Error adding account: {e}[/red]")

    async def show_settings(self):
        """Show current settings"""
        console.print("\n[bold]Current Settings:[/bold]")
        console.print(f"  • LLM Model: {self.config['llm']['model']}")
        console.print(f"  • Leak Detection: {'Enabled' if self.config['leak_detection']['enabled'] else 'Disabled'}")
        console.print(f"  • Check Interval: {self.config['leak_detection']['check_interval']} hours")
        console.print(f"  • Debug Mode: {'On' if self.config['app']['debug'] else 'Off'}")

        # Show Tor status if available
        if self.tor_manager:
            tor_status = await self.tor_manager.get_status()
            tor_running = "Running" if tor_status['running'] else "Stopped"
            tor_color = "green" if tor_status['running'] else "red"
            console.print(f"  • Tor Status: [{tor_color}]{tor_running}[/{tor_color}]")
            console.print(f"  • Darkweb Scanning: {'Enabled' if self.config['leak_detection']['sources']['darkweb']['enabled'] else 'Disabled'}")

    async def tor_management_menu(self):
        """Tor management submenu"""
        while True:
            console.print("\n[bold]Tor Management:[/bold]")

            # Get current status
            tor_status = await self.tor_manager.get_status()
            status_color = "green" if tor_status['running'] else "red"
            status_text = "Running" if tor_status['running'] else "Stopped"

            console.print(f"Current Status: [{status_color}]{status_text}[/{status_color}]")
            console.print(f"Proxy: {tor_status['proxy_host']}:{tor_status['proxy_port']}")

            if tor_status['running']:
                console.print("1. Stop Tor")
                console.print("2. Restart Tor")
                console.print("3. New circuit")
                console.print("4. Show detailed status")
                console.print("5. Back to main menu")

                try:
                    choice = console.input("\nSelect option (1-5): ").strip()

                    if choice == "1":
                        console.print("[yellow]Stopping Tor...[/yellow]")
                        await self.tor_manager.stop_tor()
                        console.print("[green]Tor stopped[/green]")

                    elif choice == "2":
                        console.print("[yellow]Restarting Tor...[/yellow]")
                        await self.tor_manager.stop_tor()
                        await asyncio.sleep(2)
                        if await self.tor_manager.start_tor():
                            console.print("[green]Tor restarted successfully[/green]")
                        else:
                            console.print("[red]Failed to restart Tor[/red]")

                    elif choice == "3":
                        console.print("[yellow]Requesting new circuit...[/yellow]")
                        if await self.tor_manager.new_circuit():
                            console.print("[green]New circuit established[/green]")
                        else:
                            console.print("[red]Failed to create new circuit[/red]")

                    elif choice == "4":
                        await self._show_tor_detailed_status(tor_status)

                    elif choice == "5":
                        break

                    else:
                        console.print("[red]Invalid option[/red]")

                except Exception as e:
                    console.print(f"[red]Error: {e}[/red]")

            else:
                console.print("1. Start Tor")
                console.print("2. Back to main menu")

                try:
                    choice = console.input("\nSelect option (1-2): ").strip()

                    if choice == "1":
                        console.print("[yellow]Starting Tor...[/yellow]")
                        if await self.tor_manager.start_tor():
                            console.print("[green]Tor started successfully[/green]")
                        else:
                            console.print("[red]Failed to start Tor[/red]")

                    elif choice == "2":
                        break

                    else:
                        console.print("[red]Invalid option[/red]")

                except Exception as e:
                    console.print(f"[red]Error: {e}[/red]")

    async def _show_tor_detailed_status(self, tor_status):
        """Show detailed Tor status"""
        console.print("\n[bold]Detailed Tor Status:[/bold]")
        for key, value in tor_status.items():
            console.print(f"  • {key.replace('_', ' ').title()}: {value}")

    def signal_handler(self, signum, frame):
        """Handle shutdown signals"""
        self.running = False
        console.print("\n[yellow]Received shutdown signal...[/yellow]")

    async def cleanup_components(self):
        """Cleanup all application components"""
        console.print("[yellow]Cleaning up components...[/yellow]")

        # Close leak detector (has aiohttp session)
        if self.leak_detector:
            try:
                await self.leak_detector.close()
                self.logger.info("Leak detector closed")
            except Exception as e:
                self.logger.error(f"Error closing leak detector: {e}")

        # Close Tor manager
        if self.tor_manager:
            try:
                await self.tor_manager.close()
                self.logger.info("Tor manager closed")
            except Exception as e:
                self.logger.error(f"Error closing Tor manager: {e}")

        # Close database manager
        if self.db_manager:
            try:
                await self.db_manager.close()
                self.logger.info("Database manager closed")
            except Exception as e:
                self.logger.error(f"Error closing database manager: {e}")

        console.print("[green]Cleanup completed[/green]")

    async def run(self):
        """Main application run method"""
        # Load configuration
        self.load_config()

        # Setup logging
        self.setup_logging()

        # Display banner
        self.display_banner()

        # Setup signal handlers
        signal.signal(signal.SIGINT, self.signal_handler)
        signal.signal(signal.SIGTERM, self.signal_handler)

        try:
            # Initialize components
            await self.initialize_components()

            # Start main loop
            self.running = True
            await self.interactive_mode()

        except Exception as e:
            console.print(f"[red]Fatal error: {e}[/red]")
            self.logger.error(f"Fatal error: {e}")
            sys.exit(1)
        finally:
            # Cleanup all components
            await self.cleanup_components()

def main():
    """Main entry point"""
    app = PassChangerApp()
    asyncio.run(app.run())

if __name__ == "__main__":
    main()
