app:
  name: PassChanger
  version: 1.0.0
  debug: true
  log_level: INFO
llm:
  provider: ollama
  model: deepseek-r1:32b
  base_url: http://localhost:11434
  timeout: 30
  max_tokens: 2048
database:
  type: sqlite
  path: data/passchanger.db
  backup_interval: 24
security:
  encryption_key_file: data/encryption.key
  password_length: 16
  password_complexity: true
  session_timeout: 3600
leak_detection:
  enabled: true
  check_interval: 24
  sources:
    haveibeenpwned:
      enabled: false
      api_key: ''
      user_agent: PassChanger-LeakDetector
    darkweb:
      enabled: true
      tor_proxy: socks5://127.0.0.1:9050
      tor_control_port: 9051
      tor_control_password: ''
      auto_start_tor: true
      tor_data_dir: data/tor
      circuit_timeout: 30
      max_retries: 3
      sites:
      - http://3g2upl4pq6kufc4m.onion
      search_terms:
      - database dump
      - leaked passwords
      - credentials
      - email list
    custom_searches:
      enabled: true
      search_engines:
      - google
      - bing
      - duckduckgo
      max_results_per_engine: 10
monitoring:
  email_alerts: false
  desktop_notifications: true
  log_file: logs/passchanger.log
  max_log_size: 10MB
accounts:
  storage_file: data/accounts.enc
  categories:
  - critical
  - important
  - standard
  - low
password_agent:
  enabled: false
  browser: chromium
  headless: false
  timeout: 30
  retry_attempts: 3
scraping:
  user_agents:
  - Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36
  - Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36
  - Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36
  request_delay: 1
  max_retries: 3
  timeout: 10
